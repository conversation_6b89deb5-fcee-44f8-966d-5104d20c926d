<html>
<head>
<meta charset="utf-8" /> 
<script>
var authUrl = 'auth.php'; // url of 'auth.php' from php-api-auth
var clientId = 'default'; // client id as defined in php-api-auth
var audience = 'api.php'; // api audience as defined in php-api-auth
window.onload = function () {
    var match = RegExp('[#&]access_token=([^&]*)').exec(window.location.hash);
    var accessToken = match && decodeURIComponent(match[1].replace(/\+/g, ' '));
    if (!accessToken) {
        document.location = authUrl+'?audience='+audience+'&response_type=token&client_id='+clientId+'&redirect_uri='+document.location.href;
    } else {
        document.location.hash = '';
        var req = new XMLHttpRequest();
        req.onreadystatechange = function () {
            if (req.readyState==4) {
                console.log(req.responseText);
                document.getElementById('output').innerHTML = JSON.stringify(JSON.parse(req.responseText), undefined, 4);
            }
        }
        url = 'api.php/records/posts?join=categories&join=tags&join=comments&filter=id,eq,1';
        req.open("GET", url, true);
        req.setRequestHeader('X-Authorization', 'Bearer '+accessToken);
        req.send();
    }
};
</script>
</head>
<body>
<pre id="output"></pre>
</body>
</html>
